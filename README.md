# RVMPlus Rewards - Recycling Rewards Mobile App

A comprehensive Vue 3 mobile application for recycling rewards and environmental impact tracking, built with modern web technologies and responsive design.

## 🌟 Features

### Core Functionality
- **User Authentication**: Username/password login system
- **Responsive Design**: Optimized for mobile and desktop devices
- **Multi-language Support**: English, Malay, and Chinese localization
- **Dark/Light Mode**: Theme switching with system preference detection

### Main Features
- **🏠 Home Dashboard**: Points display, highlights, and news updates
- **🌳 Forest Visualization**: Dynamic tree growth based on recycling progress
- **📍 Location Services**: Find nearby RVM locations with geolocation
- **🎫 Voucher System**: Redeem and manage vouchers with QR codes
- **📊 Transaction History**: Track recycling activities and rewards
- **🏃 Movement Challenges**: Time-based challenges for increased engagement
- **🔍 QR Code Scanner**: Scan QR codes for payments and interactions
- **👤 Profile Management**: Edit personal information and preferences
- **🌤️ Weather Integration**: Location-based weather visualization
- **📱 In-App Browser**: Contact us, help & support, and privacy policy pages

### Technical Features
- **API Integration**: Real-time data from RVMPlus API with bitmap protocol
- **State Management**: Pinia stores for efficient data caching
- **Environment Configuration**: Separate configs for development, staging, and production
- **Progressive Web App**: Optimized for mobile installation
- **Comprehensive Testing**: Playwright tests for mobile and desktop views

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- npm or yarn package manager

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd RewardRedemption

# Install dependencies
npm install

# Start development server
npm run dev
```

### Available Scripts

```bash
# Development
npm run dev                    # Start development server

# Building
npm run build                  # Build for production
npm run build:staging          # Build for staging environment
npm run build:production       # Build for production environment
npm run preview                # Preview production build

# Testing
npm run test                   # Run Playwright tests
npm run test:ui                # Run tests with UI
npm run test:headed            # Run tests in headed mode

# Code Quality
npm run lint                   # Run ESLint
npm run lint:fix               # Fix ESLint issues
npm run format                 # Format code with Prettier
```

## 🌍 Environment Configuration

The application supports multiple environments with different API endpoints:

- **Development**: Local development with mock data
- **Staging**: `staging.rvmplus.com` for testing
- **Production**: `api.rvmplus.com` for live deployment

See [ENV_CONFIG.md](./ENV_CONFIG.md) for detailed environment setup instructions.

## 🧪 Testing

The project includes comprehensive Playwright tests covering:
- Authentication flows
- Responsive design across devices
- Navigation functionality
- Component interactions
- API integrations

### Test Credentials
- **Phone**: 60102431439
- **Password**: 123456

## 🚀 Deployment

### Vercel (Recommended)

The project is optimized for Vercel deployment with automatic environment detection:

1. **Automatic Deployment**:
   - Connect your GitHub repository to Vercel
   - Vercel automatically detects Vite configuration
   - Environment-specific builds based on branch

2. **Manual Deployment**:
   ```bash
   # Install Vercel CLI
   npm install -g vercel

   # Login and deploy
   vercel login
   vercel

   # Production deployment
   vercel --prod
   ```

### Environment Variables for Deployment

Configure these environment variables in your deployment platform:

```bash
VITE_API_BASE_URL=https://staging.rvmplus.com  # or production URL
VITE_USE_MOCK_DATA=false
VITE_APP_NAME=RVMPlus Rewards
VITE_APP_VERSION=1.1.0
```

## 🏗️ Project Structure

```
src/
├── components/          # Reusable Vue components
│   ├── BottomNavigation.vue
│   ├── Header.vue
│   ├── Highlights.vue
│   └── ...
├── views/              # Page components
│   ├── Home.vue
│   ├── Profile.vue
│   ├── Forest.vue
│   └── ...
├── stores/             # Pinia state management
│   ├── auth.js
│   ├── theme.js
│   └── ...
├── services/           # API service layers
│   ├── authService.js
│   ├── bitmapService.js
│   └── ...
├── composables/        # Vue composition functions
├── utils/              # Utility functions
├── i18n/               # Internationalization
└── data/               # Static data files
```

## 🛠️ Technology Stack

- **Frontend Framework**: Vue 3 with Composition API
- **Build Tool**: Vite 6.3.1
- **State Management**: Pinia 3.0.2
- **Routing**: Vue Router 4.5.0
- **HTTP Client**: Axios 1.9.0
- **Internationalization**: Vue I18n 9.14.4
- **QR Code**: QRCode.vue 3.6.0 & Vue QRCode Reader 5.7.2
- **Testing**: Playwright 1.52.0
- **Code Quality**: ESLint 9.25.1 + Prettier 3.5.3
- **Styling**: CSS3 with custom properties for theming

## 📱 Mobile Features

### Bottom Navigation
- Custom U-curve design supporting dark/light modes
- Responsive height adjustment
- Active state indicators

### Responsive Design
- Mobile-first approach
- Tablet and desktop optimizations
- Touch-friendly interactions
- Swipe gestures for carousels

### Progressive Web App
- Installable on mobile devices
- Offline capability for cached content
- App-like experience

## 🔧 API Integration

The application integrates with the RVMPlus API using a bitmap protocol for efficient data transfer:

### Key Endpoints
- **Authentication**: Login with username/password
- **User Profile**: Get and update user information
- **Points & Balance**: Real-time points and balance data
- **Transactions**: 3-month transaction history
- **Locations**: Nearby RVM locations with geolocation
- **Vouchers**: Available and redeemed vouchers
- **Forest Stats**: Environmental impact tracking

### Data Caching
- Pinia stores cache API responses
- Optimized API calls to prevent redundant requests
- Real-time updates when data changes

## 🌐 Internationalization

The app supports three languages:
- **English** (en)
- **Malay** (ms)
- **Chinese** (zh)

Language files are located in `src/i18n/locales/` and can be easily extended for additional languages.

## 🎨 Theming

### Dark/Light Mode
- Automatic system preference detection
- Manual theme switching
- Consistent theming across all components
- CSS custom properties for easy customization

### Design System
- Rounded corners for modern UI
- Consistent spacing and typography
- Accessible color contrasts
- Mobile-optimized touch targets

## 📋 Development Guidelines

### Code Style
- ESLint configuration for Vue 3
- Prettier for code formatting
- Consistent naming conventions
- Component composition patterns

### Git Workflow
- Feature branch development
- Conventional commit messages
- Automated testing on pull requests
- Version tagging for releases

### Performance
- Lazy loading for route components
- Optimized bundle splitting
- Image optimization
- Efficient API caching

## 🔍 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clear node_modules and reinstall
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **API Connection Issues**
   - Check environment variables
   - Verify API endpoint accessibility
   - Review CORS configuration

3. **Mobile Testing**
   - Use browser dev tools for mobile simulation
   - Test on actual devices when possible
   - Check responsive breakpoints

### Debug Mode
Enable debug logging by setting:
```bash
VITE_DEBUG=true
```

## 📚 Documentation

- [Environment Setup](./ENV_CONFIG.md)
- [API Requirements](./API_Requirements.md)
- [Error Codes](./API_Error_Codes.md)
- [Changelog](./CHANGELOG.md)
- [In-App Browser](./README-in-app-browser.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆔 Version

Current version: **1.1.0**

For detailed version history, see [CHANGELOG.md](./CHANGELOG.md).

---

**RVMPlus Rewards** - Making recycling rewarding and environmental impact visible. 🌱
